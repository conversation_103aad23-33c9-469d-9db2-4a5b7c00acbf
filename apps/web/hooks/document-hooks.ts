import { buildChunkHighlights } from "@/lib/doc-utils";
import { IReferenceChunk } from "@/service/session-service";
import { useState, useMemo } from "react";
import { IHighlight } from 'react-pdf-highlighter';

export const useGetChunkHighlights = (
  selectedChunk: IReferenceChunk,
) => {
  const [size, setSize] = useState({ width: 849, height: 1200 });

  const highlights: IHighlight[] = useMemo(() => {
    return buildChunkHighlights(selectedChunk, size);
  }, [selectedChunk, size]);

  const setWidthAndHeight = (width: number, height: number) => {
    setSize((pre) => {
      if (pre.height !== height || pre.width !== width) {
        return { height, width };
      }
      return pre;
    });
  };

  return { highlights, setWidthAndHeight };
};