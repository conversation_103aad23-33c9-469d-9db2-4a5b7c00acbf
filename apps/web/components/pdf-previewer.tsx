"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@ragtop-web/ui/components/button'
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  Sheet<PERSON>eader, 
  Sheet<PERSON>itle, 
  SheetTrigger 
} from '@ragtop-web/ui/components/sheet'
import { 
  ZoomIn, 
  ZoomOut, 
  RotateCw, 
  Download, 
  X,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { IReferenceChunk } from '@/service/session-service'

interface PdfPreviewerProps {
  url: string
  fileName: string
  trigger?: React.ReactNode
  open?: boolean
  chunk: IReferenceChunk
  onOpenChange?: (open: boolean) => void
}



/**
 * PDF 预览组件
 * 
 * 基于抽屉形式展示 PDF 预览
 */
export function PdfPreviewer({ 
  url="http://*************:9380/api/v1/ragtop/document/get/a4f2779c3bbd11f0b2071a70a19bcd0e", 
  fileName, 
  trigger, 
  open, 
  chunk,
  onOpenChange 
}: PdfPreviewerProps) {
  const [scale, setScale] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
    const { highlights: state, setWidthAndHeight } = useGetChunkHighlights(chunk);


  // 重置状态
  const resetState = () => {
    setScale(1)
    setRotation(0)
    setCurrentPage(1)
    setIsLoading(true)
    setError(null)
  }

  // 缩放控制
  const handleZoomIn = () => setScale(prev => Math.min(prev + 0.25, 3))
  const handleZoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.5))
  
  // 旋转控制
  const handleRotate = () => setRotation(prev => (prev + 90) % 360)
  
  // 页面导航
  const handlePrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1))
  const handleNextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages))

  // 下载文件
  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 当抽屉打开时重置状态
  useEffect(() => {
    if (open) {
      resetState()
    }
  }, [open])


  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {trigger}
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-4xl p-0">
        <div className="flex flex-col h-full">
          {/* 头部工具栏 */}
          <SheetHeader className="px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div>
                <SheetTitle className="text-lg font-semibold">{fileName}</SheetTitle>
                <SheetDescription className="text-sm text-muted-foreground">
                  PDF 文档预览
                </SheetDescription>
              </div>
              
              {/* 工具栏按钮 */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomOut}
                  disabled={scale <= 0.5}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>
                
                <span className="text-sm font-medium min-w-[60px] text-center">
                  {Math.round(scale * 100)}%
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomIn}
                  disabled={scale >= 3}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRotate}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {/* 页面导航 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevPage}
                  disabled={currentPage <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                
                <span className="text-sm">
                  {currentPage} / {totalPages}
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </SheetHeader>

          {/* PDF 内容区域 */}
          <div className="flex-1 overflow-auto bg-gray-100 dark:bg-gray-900">
            <div className="flex items-center justify-center min-h-full p-4">
              {isLoading && (
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">加载中...</p>
                </div>
              )}
              
              {error && (
                <div className="text-center">
                  <p className="text-sm text-destructive mb-2">加载失败</p>
                  <p className="text-xs text-muted-foreground">{error}</p>
                </div>
              )}
              
              {!isLoading && !error && (
                <div 
                  className="bg-white shadow-lg"
                  style={{
                    transform: `scale(${scale}) rotate(${rotation}deg)`,
                    transformOrigin: 'center',
                    transition: 'transform 0.2s ease-in-out'
                  }}
                >
                  <iframe
                    src={`${url}`}
                    className="w-[800px] h-[1000px] border-0"
                    title={'fileName'}
                    onLoad={() => setIsLoading(false)}
                    onError={() => {
                      setIsLoading(false)
                      setError('无法加载 PDF 文件')
                    }}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
