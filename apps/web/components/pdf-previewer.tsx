"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import '@/styles/pdf-highlighter.css'
import { Button } from '@ragtop-web/ui/components/button'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@ragtop-web/ui/components/sheet'
import {
  ZoomIn,
  ZoomOut,
  RotateCw,
  Download,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { IReferenceChunk } from '@/service/session-service'
import { useGetChunkHighlights } from '@/hooks/document-hooks'
import { PdfLoader, PdfHighlighter, IHighlight, NewHighlight, Popup, AreaHighlight } from 'react-pdf-highlighter'

interface PdfPreviewerProps {
  url: string
  fileName: string
  trigger?: React.ReactNode
  open?: boolean
  chunk: IReferenceChunk
  onOpenChange?: (open: boolean) => void
}



/**
 * PDF 预览组件
 *
 * 基于抽屉形式展示 PDF 预览
 */
export function PdfPreviewer({
  url="http://*************:9380/api/v1/ragtop/document/get/a4f2779c3bbd11f0b2071a70a19bcd0e",
  fileName,
  trigger,
  open,
  chunk,
  onOpenChange
}: PdfPreviewerProps) {
  const [scale, setScale] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const { highlights, setWidthAndHeight } = useGetChunkHighlights(chunk)
  const scrollViewerTo = useRef<(highlight: IHighlight) => void>(() => {})


  const addHighlight = useCallback((highlight: NewHighlight) => {
    // 这里可以添加新高亮的逻辑，目前只显示现有高亮
    console.log('Add highlight:', highlight)
  }, [])

  const updateHighlight = useCallback((
    highlightId: string,
    position: Partial<IHighlight['position']>,
    content: Partial<IHighlight['content']>
  ) => {
    // 这里可以添加更新高亮的逻辑
    console.log('Update highlight:', highlightId, position, content)
  }, [])

  // 重置状态
  const resetState = () => {
    setScale(1)
    setRotation(0)
    setCurrentPage(1)
  }

  // 缩放控制
  const handleZoomIn = () => setScale(prev => Math.min(prev + 0.25, 3))
  const handleZoomOut = () => setScale(prev => Math.max(prev - 0.25, 0.5))

  // 旋转控制
  const handleRotate = () => setRotation(prev => (prev + 90) % 360)

  // 页面导航
  const handlePrevPage = () => setCurrentPage(prev => Math.max(prev - 1, 1))
  const handleNextPage = () => setCurrentPage(prev => Math.min(prev + 1, totalPages))

  // 下载文件
  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 当抽屉打开时重置状态
  useEffect(() => {
    if (open) {
      resetState()
    }
  }, [open])




  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {trigger}
      </SheetTrigger>
      <SheetContent side="right" className="w-full sm:max-w-4xl p-0">
        <div className="flex flex-col h-full">
          {/* 头部工具栏 */}
          <SheetHeader className="px-6 py-4 border-b">
            <div className="flex items-center justify-between">
              <div>
                <SheetTitle className="text-lg font-semibold">{fileName}</SheetTitle>
                <SheetDescription className="text-sm text-muted-foreground">
                  PDF 文档预览
                </SheetDescription>
              </div>

              {/* 工具栏按钮 */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomOut}
                  disabled={scale <= 0.5}
                >
                  <ZoomOut className="h-4 w-4" />
                </Button>

                <span className="text-sm font-medium min-w-[60px] text-center">
                  {Math.round(scale * 100)}%
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleZoomIn}
                  disabled={scale >= 3}
                >
                  <ZoomIn className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRotate}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDownload}
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* 页面导航 */}
            {totalPages > 1 && (
              <div className="flex items-center justify-center gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePrevPage}
                  disabled={currentPage <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>

                <span className="text-sm">
                  {currentPage} / {totalPages}
                </span>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage >= totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            )}
          </SheetHeader>

          {/* PDF 内容区域 */}
          <div className="flex-1 overflow-hidden bg-gray-100 dark:bg-gray-900">
            <div className="h-full">
              <PdfLoader url={url} beforeLoad={
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">加载中...</p>
                  </div>
                </div>
              }>
                {(pdfDocument) => {
                  // PDF 文档加载完成后，设置页面尺寸
                  useEffect(() => {
                    if (pdfDocument) {
                      pdfDocument.getPage(1).then((page) => {
                        const viewport = page.getViewport({ scale: 1 })
                        setWidthAndHeight(viewport.width, viewport.height)
                        setTotalPages(pdfDocument.numPages)
                      })
                    }
                  }, [pdfDocument])

                  return (
                  <div
                    style={{
                      transform: `scale(${scale}) rotate(${rotation}deg)`,
                      transformOrigin: 'center',
                      transition: 'transform 0.2s ease-in-out',
                      height: '100%'
                    }}
                  >
                    <PdfHighlighter
                      pdfDocument={pdfDocument}
                      enableAreaSelection={(event) => event.altKey}
                      onScrollChange={() => {}}
                      scrollRef={(scrollTo) => {
                        scrollViewerTo.current = scrollTo
                      }}
                      onSelectionFinished={(
                        position,
                        content,
                        hideTipAndSelection
                      ) => (
                        <div className="bg-white border border-gray-300 rounded shadow-lg p-2 max-w-xs">
                          <div className="text-sm text-gray-700 mb-2">
                            {content.text ? `${content.text.slice(0, 90)}...` : "选中的内容"}
                          </div>
                          <div className="flex gap-2">
                            <button
                              className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600"
                              onClick={() => {
                                addHighlight({
                                  content,
                                  position,
                                  comment: { text: "", emoji: "" }
                                })
                                hideTipAndSelection()
                              }}
                            >
                              高亮
                            </button>
                            <button
                              className="px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                              onClick={hideTipAndSelection}
                            >
                              取消
                            </button>
                          </div>
                        </div>
                      )}
                      highlightTransform={(
                        highlight,
                        index,
                        setTip,
                        hideTip,
                        viewportToScaled,
                        screenshot,
                        isScrolledTo
                      ) => {
                        const isTextHighlight = !highlight.content?.image

                        const component = isTextHighlight ? (
                          <div
                            className="bg-yellow-200 opacity-75 cursor-pointer"
                            onClick={(event) => {
                              event.stopPropagation()
                              setTip(highlight, (highlight) => (
                                <div className="bg-white border border-gray-300 rounded shadow-lg p-2 max-w-xs">
                                  <div className="text-sm text-gray-700 mb-2">
                                    {highlight.content.text}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    点击查看详情
                                  </div>
                                </div>
                              ))
                            }}
                          />
                        ) : (
                          <AreaHighlight
                            highlight={highlight}
                            isScrolledTo={isScrolledTo}
                            onChange={(boundingRect) => {
                              updateHighlight(
                                highlight.id,
                                {
                                  boundingRect: viewportToScaled(boundingRect)
                                },
                                {
                                  image: screenshot(boundingRect)
                                }
                              )
                            }}
                          />
                        )

                        return (
                          <Popup
                            popupContent={<div />}
                            onMouseOver={(popupContent) =>
                              setTip(highlight, () => popupContent)
                            }
                            onMouseOut={hideTip}
                            key={index}
                          >
                            {component}
                          </Popup>
                        )
                      }}
                      highlights={highlights}
                    />
                  </div>
                  )
                }}
              </PdfLoader>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}
